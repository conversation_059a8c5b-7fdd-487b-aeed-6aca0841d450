{"globalStyle": {"navigationStyle": "default", "navigationBarTitleText": "unibest", "navigationBarBackgroundColor": "#f8f8f8", "navigationBarTextStyle": "black", "backgroundColor": "#FFFFFF"}, "easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "pages": [{"path": "pages/index/index", "type": "home"}, {"path": "pages/accidentDetail/accidentDetail", "type": "page"}, {"path": "pages/error/error", "type": "page", "style": {"navigationStyle": "custom"}}, {"path": "pages/regulations/laws", "type": "page"}, {"path": "pages/regulations/operating", "type": "page"}, {"path": "pages/regulations/regulations", "type": "page"}, {"path": "pages/regulations/rules", "type": "page"}], "subPackages": []}