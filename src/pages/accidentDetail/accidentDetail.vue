<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '事故详情',
    navigationBarBackgroundColor: '#0087fc',
    navigationBarTextStyle: 'white',
  },
}
</route>
<template>
  <view class="accident-detail">
    <CustomTabs :tabs="tabs" :activeIndex="activeIndex" @handleClick="handleChange"></CustomTabs>
    <Detail v-if="activeIndex == 0" />
    <Manifest v-else />
  </view>
</template>

<script lang="ts" setup>
import { getAccidentDetail } from './fetch'
import CustomTabs from './components/custom-Tabs.vue'
import Detail from './components/detail.vue'
import Manifest from './components/manifest.vue'

const tabs = ref(['任务详情', '事件清单'])
const activeIndex = ref(0)
function handleChange(event) {
  activeIndex.value = event
}

const accidentInfo = ref<any>({})
const getDetailData = async (id) => {
  try {
    const res = await getAccidentDetail({ id })
    accidentInfo.value = res.data
    accidentInfo.value.accidentAddr = getSecondLastCommaData(accidentInfo.value.accidentAddr)
  } catch (error) {}
}
const getFileUrl = (item: any) => {
  const path = item.documentAddr
  if (path) {
    const p = {
      viewId: item.id,
      viewType: uni.getStorageSync('lawId'),
    }
    browseFile(p)
    sendWebview(path)
  }
}
const seeFileUrl = (value) => {
  // console.log('value = ', value)
  const path = value.reportAddr
  if (path) {
    const p = {
      viewId: value.id,
      viewType: uni.getStorageSync('lawId'),
    }
    console.log('p = ', p)
    browseFile(p)
    sendWebview(path)
  }
}
onLoad((o) => {
  // getDetailData(o.id)
})
</script>

<style lang="scss" scoped>
.accident-detail {
  min-height: 100vh;
  // padding: 10px;
  background-color: #f7f7f7;
}
</style>
