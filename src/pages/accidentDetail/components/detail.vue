<template>
  <view class="taskDetail">
    <view class="info">
      <view>
        <text>所属单位：</text>
        899采气站
      </view>
      <view>
        <text>任务时间：</text>
        班组级
      </view>
      <view>
        <text>任务状态：</text>
        进行中
      </view>
      <view>
        <text>视频巡检点位：</text>
        92个
      </view>
    </view>
    <view class="card">
      <view
        class="li"
        v-for="(item, index) in cardList"
        :key="index"
        :class="{ Active: item.isActive }"
      >
        <view class="label">{{ item.label }}</view>
        <view class="value">{{ item.value }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const cardList = ref([
  { label: '已巡检', value: 0, isActive: true },
  { label: '待巡检', value: 0, isActive: false },
  { label: '异常事件', value: 0, isActive: false },
  { label: '巡检进度', value: 0, isActive: false },
])
</script>

<style scoped lang="scss">
.Active {
  background: linear-gradient(180deg, #568eff 0%, #2167f1 100%);
  .label {
    font-size: 12px !important;
    color: #ffffff !important;
  }
  .value {
    font-size: 22px !important;
    font-weight: 500;
    color: #ffffff !important;
  }
}
.taskDetail {
  font-size: 14px;
  .info {
    padding: 10px;
    background-color: white;
    view {
      margin-bottom: 12px;
      text {
        color: #7f7f7f;
      }
    }
  }
  .card {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    margin-top: 26px;
    margin-bottom: 26px;
    background-color: white;
    .li {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 78px;
      height: 58px;
      text-align: center;
      // background: linear-gradient(180deg, #568eff 0%, #2167f1 100%);
      background-color: #ebf1ffff;
      border-radius: 8px 8px 8px 8px;
      .label {
        font-size: 12px;
        color: #0256ffff;
      }
      .value {
        font-size: 22px;
        font-weight: 500;
        color: #0256ffff;
      }
    }
  }
}
</style>
