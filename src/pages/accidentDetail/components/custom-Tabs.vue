<template>
  <view class="tabs-header">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="tabs-header-item"
      :class="{ 'tabs-header-active': activeIndex === index }"
      @click="handleClick(index)"
    >
      {{ item }}
      <view :class="{ 'tabs-header-item-active': activeIndex === index }"></view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  tabs: {
    type: Array,
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
})
const activeIndex = ref(props.activeIndex)

const $emit = defineEmits(['handleClick'])

function handleClick(index) {
  activeIndex.value = index
  $emit('handleClick', index)
}
</script>

<style lang="scss">
.tabs {
  display: flex;
  flex-direction: column;
}

.tabs-header {
  display: flex;
  margin-top: -2px;
  background-color: white;
}

.tabs-header-active {
  color: #0256ff;
}

.tabs-header-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  cursor: pointer;
}

.tabs-header-item-active {
  width: 40px;
  height: 4px;
  margin-top: 6px;
  background-color: #0256ff;
  border-radius: 1px;
}
</style>
