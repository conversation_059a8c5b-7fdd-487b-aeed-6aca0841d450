<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '事件清单',
    navigationBarBackgroundColor: '#0087fc',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <z-paging class="wrapper" ref="paging" v-model="eventList" @query="getEventList">
    <!-- 搜索区域 -->
    <wd-search
      placeholder-left
      @change="handleSearchChange"
      placeholder="请输入事件标题或关键词搜索"
      :hide-cancel="true"
      v-model="searchKeyword"
      @clear="handleSearchClear"
    />

    <!-- 筛选菜单 -->
    <wd-drop-menu style="z-index: 99">
      <wd-drop-menu-item
        title="事件状态"
        v-model="searchStatus"
        :options="statusOptions"
        @change="handleStatusChange"
      />
      <wd-drop-menu-item
        title="优先级"
        v-model="searchPriority"
        :options="priorityOptions"
        @change="handlePriorityChange"
      />
    </wd-drop-menu>

    <!-- 事件列表 -->
    <view v-if="eventList.length > 0">
      <manifest
        v-for="item in eventList"
        :key="item.id"
        :eventData="item"
        @click="handleEventClick"
      />
    </view>
    <view v-else>
      <Empty />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { ref, onMounted, onShow } from 'vue'
import Manifest from '../accidentDetail/components/manifest.vue'
import Empty from '@/components/empty.vue'

// 响应式数据
const paging = ref()
const eventList = ref<any[]>([])
const searchKeyword = ref('')
const searchStatus = ref('')
const searchPriority = ref('')

// 状态选项
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '待处理', value: '0' },
  { label: '处理中', value: '1' },
  { label: '已解决', value: '2' },
  { label: '已关闭', value: '3' },
])

// 优先级选项
const priorityOptions = ref([
  { label: '全部', value: '' },
  { label: '低', value: '1' },
  { label: '中', value: '2' },
  { label: '高', value: '3' },
  { label: '紧急', value: '4' },
])

// 分页参数
const pageParams = ref({
  pageNo: 1,
  pageSize: 10,
})

// 模拟事件数据
const mockEventData = [
  {
    id: '1',
    title: '设备异常报警事件',
    status: 0,
    statusName: '待处理',
    priority: 3,
    priorityName: '高',
    location: '1号生产线',
    reportTime: '2024-01-15 14:30:00',
    reporterName: '张三',
    description: '设备运行过程中出现异常报警，需要立即处理',
    deptName: '生产部',
  },
  {
    id: '2',
    title: '安全隐患排查',
    status: 1,
    statusName: '处理中',
    priority: 2,
    priorityName: '中',
    location: '2号车间',
    reportTime: '2024-01-15 10:15:00',
    reporterName: '李四',
    description: '发现安全隐患，正在进行排查处理',
    deptName: '安全部',
  },
  {
    id: '3',
    title: '环保检查问题',
    status: 2,
    statusName: '已解决',
    priority: 1,
    priorityName: '低',
    location: '废水处理站',
    reportTime: '2024-01-14 16:45:00',
    reporterName: '王五',
    description: '环保检查发现的问题已经整改完成',
    deptName: '环保部',
  },
  {
    id: '4',
    title: '紧急停机事件',
    status: 0,
    statusName: '待处理',
    priority: 4,
    priorityName: '紧急',
    location: '主控室',
    reportTime: '2024-01-15 15:20:00',
    reporterName: '赵六',
    description: '设备出现故障需要紧急停机检修',
    deptName: '设备部',
  },
  {
    id: '5',
    title: '质量问题反馈',
    status: 1,
    statusName: '处理中',
    priority: 2,
    priorityName: '中',
    location: '质检中心',
    reportTime: '2024-01-15 09:30:00',
    reporterName: '孙七',
    description: '产品质量检测发现问题，正在分析原因',
    deptName: '质量部',
  },
]

// 获取事件列表
const getEventList = async (pageNo: number) => {
  try {
    uni.showLoading({ title: '加载中...', mask: true })
    
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 过滤数据
    let filteredData = [...mockEventData]
    
    // 关键词搜索
    if (searchKeyword.value) {
      filteredData = filteredData.filter(item => 
        item.title.includes(searchKeyword.value) || 
        item.description.includes(searchKeyword.value)
      )
    }
    
    // 状态筛选
    if (searchStatus.value) {
      filteredData = filteredData.filter(item => item.status.toString() === searchStatus.value)
    }
    
    // 优先级筛选
    if (searchPriority.value) {
      filteredData = filteredData.filter(item => item.priority.toString() === searchPriority.value)
    }
    
    // 分页处理
    const startIndex = (pageNo - 1) * pageParams.value.pageSize
    const endIndex = startIndex + pageParams.value.pageSize
    const pageData = filteredData.slice(startIndex, endIndex)
    
    paging.value.complete(pageData)
    uni.hideLoading()
  } catch (error) {
    console.error('获取事件列表失败:', error)
    paging.value.complete(false)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  }
}

// 搜索变化
const handleSearchChange = (value: any) => {
  searchKeyword.value = value.value
  paging.value.reload()
}

// 清除搜索
const handleSearchClear = () => {
  searchKeyword.value = ''
  paging.value.reload()
}

// 状态变化
const handleStatusChange = (event: any) => {
  searchStatus.value = event.selectedItem.value
  paging.value.reload()
}

// 优先级变化
const handlePriorityChange = (event: any) => {
  searchPriority.value = event.selectedItem.value
  paging.value.reload()
}

// 点击事件项
const handleEventClick = (item: any) => {
  console.log('点击事件:', item)
  uni.showToast({
    title: `点击了事件: ${item.title}`,
    icon: 'none',
  })
}

// 生命周期
onMounted(() => {
  console.log('事件清单页面挂载')
})

onShow(() => {
  console.log('事件清单页面显示')
  paging.value?.reload()
})
</script>

<style lang="scss" scoped>
.wrapper {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 10px;
  background-color: #f7f7f7;
}

// 确保下拉菜单样式正确
:deep(.wd-drop-menu) {
  z-index: 99;
}

:deep(.wd-drop-menu-item) {
  z-index: 100;
}
</style>
