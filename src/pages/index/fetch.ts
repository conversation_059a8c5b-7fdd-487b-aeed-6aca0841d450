import { http } from '@/utils/http'

const SERVER: string = import.meta.env.VITE_SERVER_PORT
// 事故列表
export const getPageViewTaskAPI = (param: any) => {
  return http.post<any>(`${SERVER}/video/task/pageViewTask`, param)
}

// 事故类型
export const getCategory = (param: any) => {
  return http.get<any>(`${SERVER}/category/list`, { ...param })
}

// 事故等级
export const getLevel = (param: any) => {
  return http.get<any>(`${SERVER}/level/list`, { ...param })
}
