import { useUserStore, useAppStore, useAppReceiveStore } from '@/store'
import { currRoute, getUrlObj } from '@/utils/index'
import { parseQueryString } from '@/hooks/getParams'
import { http } from '@/utils/http'

export function wvSubscribe() {
  console.log('wvSubscribe>>>>init')

  const u = decodeURIComponent(window.location.href)
  const paramData: any = parseQueryString(u)
  if (paramData.location) {
    uni.setStorageSync('locationCustom', JSON.parse(paramData.location))
  }
  uni.setStorageSync('FromParentParam', paramData)
  const appStore = useAppStore()
  const receiveStore = useAppReceiveStore()

  // 监听UniAppJSBridgeReady
  document.addEventListener('UniAppJSBridgeReady', function () {
    console.log('onLaunch>>>>')

    // 设置当前环境
    // uni.webView.getEnv(function (res) {
    //   console.log('onLaunch>当前环境：' + JSON.stringify(res))
    //   // appStore.setAppEnv(res)
    // })
  })

  // 全局监听外部应用消息
  window.$receiveData = function (data) {
    receiveStore.setAppReceiveData(data)
  }
}

export async function beforeEnter() {
  const userStore = useUserStore()
  const appStore = useAppStore()

  // 解决下面path初始获取不到的问题
  const _herf = window.location.href
  const u = decodeURIComponent(window.location.href)
  const paramData: any = parseQueryString(u)
  console.log('herf>>>>', paramData)
  const { redirect, safetyArea, ticket, syscode, appEnv, osName, flag } = paramData // 设置当前环境

  // const { path, query } = getUrlObj(_herf)
  // const { redirect, safetyArea, ticket, syscode, appEnv, osName, flag } = urlParamToJson(query) // 设置当前环境
  // // 处理redirectTo方法，内部重定向页面
  // if (redirect) return

  appStore.setAppEnv({
    plus: appEnv === 'app',
    miniprogram: appEnv === 'mp-weixin',
    osAndroid: osName === 'android',
    osIos: osName === 'ios',
    changhangh5: appEnv !== 'app' && appEnv !== 'mp-weixin',
  })

  // if (JSON.stringify(safetyArea) !== '{}') {
  //   appStore.setAppInfo(safetyArea)
  // }
  // 设置用户ticket
  if (paramData.ticket) {
    // userStore.setUserTicket(ticket)
    // 3. 根据userTicket获取token
    try {
      const userInfoStore = useUserStore()
      // const u = decodeURIComponent(window.location.href)
      // const paramData: any = parseQueryString(u)
      uni.setStorageSync('FromParentParam', paramData)
      console.log('paramData = ', paramData)
      const p = {
        sysCode: paramData.syscode,
        token: paramData.ticket,
      }
      const res: any = await http.get(
        import.meta.env.VITE_SERVER_PORT + '/login/getTokenLoginInfoByToken',
        p,
      )
      if (res.code === 'success' || res.code === '200') {
        res.data.userToken = res.data.token
        uni.setStorageSync('@userInfo', res.data)
        userInfoStore.userInfo = res.data
      } else {
        uni.showToast({
          title: res.message,
          icon: 'none',
          duration: 2000,
        })
      }
    } catch (err) {
      console.log('onLaunch中错误', err)
    }
  } else {
    uni.navigateTo({ url: '/pages/error/error' })
  }
}
