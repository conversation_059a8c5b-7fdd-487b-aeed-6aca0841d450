export const sendWebview = (path) => {
  // #ifdef H5
  uni.webView.getEnv(function (env: any) {
    console.log('当前环境：' + JSON.stringify(env))
    if (env.miniprogram) {
      uni.webView.navigateTo({
        url: '/pages/common/preview?pdf=' + path,
      })
    } else if (env.plus) {
      uni.webView.postMessage({
        data: {
          action: 'preview',
          type: 'pdf',
          res: path,
        },
      })
    }
  })
  // #endif
}
