import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppReceiveStore = defineStore(
  'appReceive',
  () => {
    // 外部应用动态传入的值
    const appData = ref<any>(null)

    const setAppReceiveData = (val: any) => {
      // console.log('setAppReceiveData', typeof val)
      // console.log('setAppReceiveData', typeof val.data, val.data)
      appData.value = val
    }

    const clearAppReceiveData = () => {
      appData.value = null
    }

    return {
      appData,
      setAppReceiveData,
      clearAppReceiveData,
    }
  },
  {
    persist: false,
  },
)
