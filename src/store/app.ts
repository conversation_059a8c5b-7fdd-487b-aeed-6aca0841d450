import { defineStore } from 'pinia'
import { ref } from 'vue'

const initState = {
  top: 0,
  bottom: 0,
}

const initEnv = {
  plus: false,
  miniprogram: false,
  osAndroid: false,
  osIos: false,
  changhangh5: false,
}

export const useAppStore = defineStore(
  'app',
  () => {
    // 引用外部safetyArea
    const appInfo = ref<any>({ ...initState })
    const setAppInfo = (val: any) => {
      appInfo.value = val
    }
    const clearAppInfo = () => {
      appInfo.value = { ...initState }
    }
    // 外部应用环境
    const appEnv = ref<any>({ ...initEnv })
    const setAppEnv = (val: any) => {
      appEnv.value = val
    }
    const clearAppEnv = () => {
      appEnv.value = { ...initEnv }
    }

    const clearApp = () => {
      appInfo.value = { ...initState }
      appEnv.value = { ...initEnv }
    }

    return {
      appInfo,
      setAppInfo,
      clearAppInfo,
      appEnv,
      setAppEnv,
      clearAppEnv,
      clearApp,
    }
  },
  {
    persist: true,
  },
)
